import ShortLongName from '../Generic/ShortLongName/ShortLongName';

export interface ISecurityLevels {
  id: number;
  name: string;
  roleIds: number[];
}

export const securityLevels: ISecurityLevels[] = [
  {
    id: 1,
    name: 'Company',
    roleIds: [7],
  },
  {
    id: 2,
    name: 'Division',
    roleIds: [6, 8, 9],
  },
  {
    id: 3,
    name: 'Client',
    roleIds: [1, 2, 3, 4, 5, 11],
  },
  {
    id: 4,
    name: 'Driver',
    roleIds: [10],
  },
];

export default securityLevels;
