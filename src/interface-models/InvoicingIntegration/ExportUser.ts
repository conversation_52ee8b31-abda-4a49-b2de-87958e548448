// Types for Invoicing Integration

/**
 * Export type for integration.
 */
export enum ExportTypeEnum {
  BUSINESS_CENTRAL = 'BUSINESS_CENTRAL',
  QUICKBOOKS = 'QUICKBOOKS',
}

/**
 * Entity export type for integration.
 */
export enum EntityExportType {
  PUSH_ALL_ENTITIES = 'PUSH_ALL_ENTITIES',
  PULL_CUSTOMERS = 'PULL_CUSTOMERS',
  PULL_ALL_ENTITIES = 'PULL_ALL_ENTITIES',
}

/**
 * Financial export type for integration.
 */
export enum FinancialExportType {
  EXPORT_CUSTOMER_INVOICES = 'EXPORT_CUSTOMER_INVOICES',
  EXPORT_FAO_PAYMENTS = 'EXPORT_FAO_PAYMENTS',
  EXPORT_ALL_TRANSACTIONS = 'EXPORT_ALL_TRANSACTIONS',
}

/**
 * Represents a destination company for export user integration.
 */
export interface DestinationCompany {
  id: string;
  name: string;
}

/**
 * Represents an export user integration configuration.
 */
export class ExportUser {
  id?: string;
  company: string;
  division: string;
  name: string;
  exportType: ExportTypeEnum;
  tenantId: string;
  environmentName: string;
  companyName: string;
  companyId: string;
  startDate: number;
  endDate: number;
  entityExportType: EntityExportType;
  financialExportType: FinancialExportType;
  accessToken: string;
  accessTokenExpiry: number;
  refreshToken: string;
  refreshTokenExpiry: number;
  uniqueApiReference: string;

  constructor() {
    this.company = '';
    this.division = '';
    this.name = '';
    this.exportType = ExportTypeEnum.BUSINESS_CENTRAL;
    this.tenantId = '';
    this.environmentName = '';
    this.companyName = '';
    this.companyId = '';
    this.startDate = 0;
    this.endDate = 0;
    this.entityExportType = EntityExportType.PULL_CUSTOMERS;
    this.financialExportType = FinancialExportType.EXPORT_CUSTOMER_INVOICES;
    this.accessToken = '';
    this.accessTokenExpiry = 0;
    this.refreshToken = '';
    this.refreshTokenExpiry = 0;
    this.uniqueApiReference = '';
  }
}

export interface CreateExportUserResponse {
  id: string;
  exportUser: ExportUser;
  companies: DestinationCompany[];
}
