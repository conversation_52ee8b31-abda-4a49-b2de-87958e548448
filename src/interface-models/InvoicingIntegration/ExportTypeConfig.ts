export interface ExportTypeConfig {
  id: string;
  exportType: string;
  authUrl: string;
  authRequestAttributes: AuthRequestAttributes;
  pkceType: string;
  tokenUrl: string;
  tokenPostData: Record<string, string>;
  tokenRequestAttributes: TokenRequestAttributes;
  apiUrl: string;
  apiHeaders: Record<string, string>;
  apiPostData: Record<string, any>;
  appId: string;
  clientId: string;
  clientSecret: string;
}

export interface AuthRequestAttributes {
  client_id: string;
  response_type: string;
  redirect_uri: string;
  response_mode: string;
  scope: string;
  state: string;
}

export interface TokenRequestAttributes {
  grant_type: string;
  refresh_token: string;
}
