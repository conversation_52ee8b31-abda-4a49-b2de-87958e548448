/**
 * Response structure for the customer list API endpoint.
 * Contains the snapshot data with customers array and metadata.
 */
export interface CustomerDataSnapshot {
  id: string;
  company: string;
  division: string;
  apiUniqueIdentifier: string;
  date: number;
  dataSnapshotType: string;
  customers: CustomerData[];
}

/**
 * Represents a customer data snapshot from Business Central export services.
 * This interface defines the structure of customer data returned from the
 * /export/getCustomerListFromExportServices endpoint.
 */
export interface CustomerData {
  id: string;
  number: string;
  name: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postcode: string;
  country: string;
  phone: string;
  email: string;
  abn: string;
  balanceDue: number;
  creditLimit: number;
  paymentTermsId?: string;
  blocked: string;
  lastModifiedDate: number;
}

export interface ExportIntegrations {
  uniqueApiReference: string;
  remoteEntityReference: string;
}
