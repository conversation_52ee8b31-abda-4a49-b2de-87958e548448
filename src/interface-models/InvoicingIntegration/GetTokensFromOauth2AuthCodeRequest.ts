interface GetTokensFromOauth2AuthCodeRequestInterface {
  access_token: string;
  refresh_token: string;
  token_id: string;
  environment_name: string;
  //company: string;
  //division: string;
}

export default class GetTokensFromOauth2AuthCodeRequest
  implements GetTokensFromOauth2AuthCodeRequestInterface
{
  constructor(
    public access_token: string = 'DEFAULT_ACCESS_TOKEN',
    public refresh_token: string = 'DEFAULT_REFRESH_TOKEN',
    public token_id: string = '',
    public environment_name: string = '',
    //public company: string = 'DEFAULT_COMPANY',
    //public division: string = 'DEFAULT_DIVISIONT'
  ) {}
}
