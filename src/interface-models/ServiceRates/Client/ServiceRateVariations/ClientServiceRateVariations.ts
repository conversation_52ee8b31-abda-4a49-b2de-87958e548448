import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';

export interface IClientServiceRateVariations {
  /**
   * Mongo id of the client service rate variations document.
   */
  _id?: string;

  /**
   * This references to the company associated with the client service rate variation.
   */
  company: string;

  /**
   * This references to the division associated with the client service rate variation.
   */
  division: string;

  /**
   * This references to a clientId associated with a service rate document (eg clientServiceRate).
   * For now (and typically) this will be '0'.
   */
  rateOwnerId: string;

  /**
   * This references to the client service rate card's tableId which is being adjusted.
   */
  rateTableId: string;

  /**
   * This references to a list of all clientIds who receive this client service rate variation
   */
  applyToIds: string[];

  /**
   * This references to the service type id for which rate is being adjusted.
   */
  serviceTypeId: number;

  /**
   * This references to the rate type id for which rate is being adjusted.
   */
  rateTypeId: JobRateType;

  /**
   * This references to the actual percentage of rate which is adjusted on client rate.
   * eg -5.00 (%) adjustment on this combination of serviceTypeId & rateTypeId.
   */
  clientAdjustmentPercentage?: number | null;

  /**
   * This references to the actual percentage of rate which is adjusted on fleet asset.
   * eg -5.00 (%) adjustment on this combination of serviceTypeId & rateTypeId
   */
  fleetAssetAdjustmentPercentage?: number | null;

  /**
   * This references to the date from which client service rate variation is active.
   */
  validFromDate: number | null;

  /**
   * This references to the date to which client service rate variation is active.
   */
  validToDate: number | null;
}

export class ClientServiceRateVariations
  implements IClientServiceRateVariations
{
  public _id: string;
  constructor(
    public company: string = '',
    public division: string = '',
    public rateOwnerId: string = '0',
    public rateTableId: string = '',
    public applyToIds: string[] = [],
    public serviceTypeId: number = 0,
    public rateTypeId: JobRateType = JobRateType.TIME,
    public clientAdjustmentPercentage: number | null = null,
    public fleetAssetAdjustmentPercentage: number | null = null,
    public validFromDate: number | null = null,
    public validToDate: number | null = null,
  ) {}

  static fromJson(
    json: Partial<IClientServiceRateVariations>,
  ): ClientServiceRateVariations {
    const instance = new ClientServiceRateVariations(
      json.company ?? '',
      json.division ?? '',
      json.rateOwnerId ?? '0',
      json.rateTableId ?? '',
      json.applyToIds ?? [],
      json.serviceTypeId ?? 0,
      json.rateTypeId ?? JobRateType.TIME,
      json.clientAdjustmentPercentage ?? null,
      json.fleetAssetAdjustmentPercentage ?? null,
      json.validFromDate ?? null,
      json.validToDate ?? null,
    );
    instance._id = json._id ?? '';
    return instance;
  }
}
