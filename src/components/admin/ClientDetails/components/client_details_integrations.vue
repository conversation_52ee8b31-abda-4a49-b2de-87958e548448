<template>
  <v-layout class="client-details-integrations" wrap>
    <v-flex md8 offset-md2 class="">
      <v-layout mt-3 row wrap>
        <v-flex md12 pb-1
          ><v-layout align-center>
            <h5 class="subheader--bold pr-3 pt-1">
              Electronic Data Interchange
            </h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout class="pt-3">
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">FTP Integration</h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-layout>
                    <v-select
                      label="Select an integration type..."
                      v-model="clientDetails.importTransformationType"
                      :items="importUserList"
                      item-text="displayName"
                      item-value="username"
                      class="v-solo-custom"
                      solo
                      :disabled="true"
                      color="light-blue"
                      flat
                    />

                    <ImportUserSelect :clientDetails="clientDetails" />
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
        <v-flex md12>
          <v-layout class="pt-3">
            <v-flex md12>
              <v-layout>
                <v-flex md4>
                  <v-layout align-center class="form-field-label-container">
                    <h6 class="subheader--faded pr-3 pb-0">API Integration</h6>
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-layout>
                    <v-select
                      label="Select an integration type..."
                      v-model="clientsApiUsers"
                      :items="apiUserList"
                      class="v-solo-custom"
                      solo
                      multiple
                      :disabled="true"
                      color="light-blue"
                      flat
                    />

                    <ApiUserSelect
                      :clientDetails="clientDetails"
                      @setClientsApiUsers="setClientsApiUsers"
                    />
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>

    <!-- Export Associations Section -->
    <v-flex md8 offset-md2>
      <v-layout mt-3 row wrap>
        <v-flex md12 pb-1>
          <v-layout align-center>
            <h5 class="subheader--bold pr-3 pt-1">Data Export Associations</h5>
            <v-divider></v-divider>
            <!-- Dynamic association button -->
            <v-btn
              small
              :color="hasExportAssociation ? 'error' : 'primary'"
              depressed
              @click="
                hasExportAssociation
                  ? removeCurrentAssociation()
                  : openAddAssociationDialog()
              "
              class="ma-2"
            >
              <v-icon size="18" class="mr-2">
                {{ hasExportAssociation ? 'delete' : 'add' }}
              </v-icon>
              {{
                hasExportAssociation ? 'Remove Association' : 'Add Association'
              }}
            </v-btn>
          </v-layout>
        </v-flex>

        <!-- Export Associations Table -->
        <v-flex md12 v-if="exportAssociationsWithDetails.length > 0">
          <v-data-table
            :headers="exportAssociationHeaders"
            :items="exportAssociationsWithDetails"
            item-key="uniqueApiReference"
            hide-actions
            class="default-table-dark gd-dark-theme"
          >
            <template v-slot:items="item">
              <tr>
                <td>
                  {{ item.item.exportUserName }}
                </td>
                <td>{{ item.item.customerName }}</td>
                <td>
                  {{ item.item.customerNumber }}
                </td>
                <!-- <td>
                  {{ item.item }}
                </td> -->
                <td>
                  {{ item.item.customerAddress }}
                </td>
              </tr>
            </template>
          </v-data-table>
        </v-flex>

        <!-- No associations message -->
        <v-flex md12 class="text-center pa-3" v-else>
          No export associations configured for this client.
        </v-flex>
      </v-layout>
    </v-flex>

    <!-- Add Association Dialog -->
    <v-dialog
      v-model="showAddAssociationDialog"
      width="900px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Add Export Association</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="closeAddAssociationDialog"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>

      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12>
          <v-flex class="body-scrollable--80 body-min-height--65">
            <v-layout column class="pa-3">
              <!-- Export User Selection -->
              <v-flex md12 class="pr-3 pl-3">
                <v-select
                  label="Select Export Integration"
                  v-model="selectedExportUser"
                  :items="availableExportUsers"
                  item-text="displayName"
                  item-value="uniqueApiReference"
                  :disabled="availableExportUsers.length === 0"
                  class="v-solo-custom"
                  solo
                  color="light-blue"
                  flat
                >
                  <template v-slot:no-data>
                    <v-list-tile>
                      <v-list-tile-content>
                        <v-list-tile-title>
                          No export integrations available with customer pull
                          capability
                        </v-list-tile-title>
                      </v-list-tile-content>
                    </v-list-tile>
                  </template>
                </v-select>
              </v-flex>

              <!-- Customer Search and Selection -->
              <v-flex v-if="selectedExportUser">
                <v-layout column>
                  <v-flex mr-2 ml-2>
                    <v-text-field
                      label="Search customers..."
                      v-model="customerSearchQuery"
                      prepend-icon="fal fa-search"
                      clearable
                      class="v-solo-custom"
                      solo
                      flat
                      depressed
                    ></v-text-field>
                  </v-flex>

                  <!-- Customer List -->
                  <v-flex v-if="filteredCustomers.length > 0">
                    <v-data-table
                      :headers="customerSelectionHeaders"
                      :items="filteredCustomers"
                      item-key="id"
                      hide-actions
                      :rows-per-page-items="[10, 25, 50]"
                      class="gd-dark-theme"
                      style="max-height: 400px; overflow-y: auto"
                    >
                      <template v-slot:items="item">
                        <tr
                          @click="selectCustomer(item.item)"
                          :class="{
                            'selected-row':
                              selectedCustomer?.id === item.item.id,
                          }"
                        >
                          <td>
                            <v-radio-group
                              v-model="selectedCustomer"
                              hide-details
                            >
                              <v-radio
                                :value="item.item"
                                color="primary"
                              ></v-radio>
                            </v-radio-group>
                          </td>
                          <td>
                            {{ item.item.name }}
                          </td>
                          <td>
                            {{ item.item.number }}
                          </td>
                          <td>
                            {{ formatCustomerAddress(item.item) }}
                          </td>
                          <td>
                            {{ item.item.phone }}
                          </td>
                          <td>
                            {{ item.item.email }}
                          </td>
                        </tr>
                      </template>
                    </v-data-table>
                  </v-flex>

                  <v-flex v-else-if="!isLoadingCustomers && selectedExportUser">
                    <v-card class="pa-3">
                      <v-card-text class="text-center">
                        No customers available. Click "Refresh Customer List" to
                        load customers from the export service.
                      </v-card-text>
                    </v-card>
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-flex>
          <v-divider></v-divider>
          <v-layout align-center pa-2>
            <v-btn outline color="error" @click="closeAddAssociationDialog">
              Cancel
            </v-btn>
            <v-spacer></v-spacer>
            <v-btn
              color="primary"
              :disabled="!selectedCustomer || !selectedExportUser"
              @click="addNewAssociation"
            >
              Add Association
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-dialog>
  </v-layout>
</template>

<script setup lang="ts">
import ApiUserSelect from '@/components/admin/ClientDetails/components/api_user_select.vue';
import ImportUserSelect from '@/components/admin/ClientDetails/components/import_user_select.vue';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import ImportUser from '@/interface-models/Generic/ImportUser';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import {
  CustomerData,
  ExportIntegrations,
} from '@/interface-models/InvoicingIntegration/CustomerDataSnapshot';
import {
  EntityExportType,
  ExportUser,
} from '@/interface-models/InvoicingIntegration/ExportUser';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { useIntegrationStore } from '@/store/modules/IntegrationStore';
import { computed, onMounted, ref } from 'vue';

// Enhanced interface for display purposes
interface ExportAssociationWithDetails extends ExportIntegrations {
  exportUserName: string;
  customerName: string;
  customerNumber: string;
  customerAddress?: string;
}

// Enhanced interface for export user display
interface ExportUserDisplay extends ExportUser {
  displayName: string;
}

const props = withDefaults(
  defineProps<{
    clientDetails: ClientDetails;
    isEdited: boolean;
  }>(),
  {
    isEdited: false,
  },
);

const dataImportStore = useDataImportStore();
const integrationStore = useIntegrationStore();

// Existing data
const clientsApiUsers = ref<string[]>([]);

// Export Associations data
const showAddAssociationDialog = ref(false);
const activeTab = ref(0);
const selectedExportUser = ref<string | null>(null);
const selectedCustomer = ref<CustomerData | null>(null);
const customerSearchQuery = ref('');
const isLoadingCustomers = ref(false);

// Local cached data for display
const cachedExportUsers = ref<ExportUser[]>([]);
const cachedCustomers = ref<CustomerData[]>([]);
const availableCustomers = ref<CustomerData[]>([]);

// Table headers for export associations
const exportAssociationHeaders = ref<TableHeader[]>([
  {
    text: 'Export User',
    value: 'exportUserName',
    align: 'left',
    sortable: true,
  },

  {
    text: 'Customer Name',
    value: 'customerName',
    align: 'left',
    sortable: true,
  },
  {
    text: 'Customer Number',
    value: 'customerNumber',
    align: 'left',
    sortable: true,
  },
  // {
  //   text: 'Remote Reference',
  //   value: 'remoteEntityReference',
  //   align: 'left',
  //   sortable: true,
  // },
  { text: 'Address', value: 'customerAddress', align: 'left', sortable: false },
]);

// Table headers for customer selection
const customerSelectionHeaders = ref<TableHeader[]>([
  {
    text: '',
    value: 'select',
    align: 'center',
    sortable: false,
    width: '50px',
  },
  { text: 'Number', value: 'number', align: 'left', sortable: true },
  { text: 'Name', value: 'name', align: 'left', sortable: true },
  { text: 'Address', value: 'address', align: 'left', sortable: false },
  { text: 'Phone', value: 'phone', align: 'left', sortable: false },
  { text: 'Email', value: 'email', align: 'left', sortable: false },
]);

// A list of all available import (FTP) users
const importUserList = computed<ImportUser[]>(() => {
  return dataImportStore.importUserList;
});

// A list of all available api users
const apiUserList = computed<string[]>(() => {
  return dataImportStore.apiUserList;
});

// Get export users that have customer pull capability (using cached data)
const availableExportUsers = computed<ExportUserDisplay[]>(() => {
  return cachedExportUsers.value
    .filter(
      (user: ExportUser) =>
        // Must have a company name set
        user.companyName &&
        user.companyName.trim() !== '' &&
        // Must have correct entity export type
        (user.entityExportType === EntityExportType.PULL_CUSTOMERS ||
          user.entityExportType === EntityExportType.PULL_ALL_ENTITIES),
    )
    .map((user: ExportUser) => ({
      ...user,
      displayName: `${user.name} - ${user.companyName}`,
    }));
});

// Check if client has an export association
const hasExportAssociation = computed<boolean>(() => {
  return (
    props.clientDetails.exportAssociations &&
    props.clientDetails.exportAssociations.length > 0
  );
});

// Enhanced export associations with customer details
const exportAssociationsWithDetails = computed<ExportAssociationWithDetails[]>(
  () => {
    if (!props.clientDetails.exportAssociations) {
      return [];
    }

    return props.clientDetails.exportAssociations.map(
      (association: ExportIntegrations) => {
        // Use cached export users instead of store directly
        const exportUser = cachedExportUsers.value.find(
          (user: ExportUser) =>
            user.uniqueApiReference === association.uniqueApiReference,
        );

        // Use cached customers instead of availableCustomers
        const customer = cachedCustomers.value.find(
          (cust: CustomerData) => cust.id === association.remoteEntityReference,
        );

        return {
          ...association,
          exportUserName: exportUser ? exportUser.name : 'Unknown Export User',
          customerName: customer ? customer.name : 'Unknown Customer',
          customerNumber: customer ? customer.number : 'Unknown',
          customerAddress: customer
            ? formatCustomerAddress(customer)
            : 'Unknown Address',
        };
      },
    );
  },
);

// Remove the current export association (one association per client)
const removeCurrentAssociation = async () => {
  // Initialize exportAssociations as empty array if null/undefined
  if (!props.clientDetails.exportAssociations) {
    props.clientDetails.exportAssociations = [];
    return;
  }

  // Clear all associations (since we only allow one)
  props.clientDetails.exportAssociations = [];

  // Automatically save to backend
  try {
    const result = await props.clientDetails.save();
    if (result) {
      showNotification('Export association removed successfully.', {
        title: 'Export Associations',
        type: HealthLevel.SUCCESS,
      });
    } else {
      showNotification('Failed to save changes to backend.', {
        title: 'Export Associations',
        type: HealthLevel.ERROR,
      });
    }
  } catch (error) {
    console.error('Error saving client details:', error);
    showNotification('Failed to save changes to backend.', {
      title: 'Export Associations',
      type: HealthLevel.ERROR,
    });
  }
};

// Filtered customers based on search
const filteredCustomers = computed(() => {
  if (!customerSearchQuery.value) {
    return availableCustomers.value;
  }

  const query = customerSearchQuery.value.toLowerCase();
  return availableCustomers.value.filter(
    (customer: CustomerData) =>
      customer.name.toLowerCase().includes(query) ||
      customer.number.toLowerCase().includes(query) ||
      customer.email.toLowerCase().includes(query) ||
      customer.addressLine1.toLowerCase().includes(query),
  );
});

// Helper function to format customer address
const formatCustomerAddress = (customer: CustomerData): string => {
  const parts = [
    customer.addressLine1,
    customer.addressLine2,
    customer.city,
    customer.state,
    customer.postcode,
  ].filter(Boolean);
  return parts.join(', ');
};

// Dialog management methods
const openAddAssociationDialog = async () => {
  // Ensure cached data is loaded before opening dialog
  if (
    cachedExportUsers.value.length === 0 ||
    cachedCustomers.value.length === 0
  ) {
    await loadExportData();
  }

  showAddAssociationDialog.value = true;
  activeTab.value = 0;
  selectedCustomer.value = null;
  customerSearchQuery.value = '';

  // Set available customers from cached data
  availableCustomers.value = [...cachedCustomers.value];

  // Auto-select export user if only one valid option is available
  const validExportUsers = availableExportUsers.value;
  if (validExportUsers.length === 1) {
    selectedExportUser.value = validExportUsers[0].uniqueApiReference;
  } else {
    selectedExportUser.value = null;
  }
};

const closeAddAssociationDialog = () => {
  showAddAssociationDialog.value = false;
  activeTab.value = 0;
  selectedExportUser.value = null;
  selectedCustomer.value = null;
  customerSearchQuery.value = '';
  availableCustomers.value = [];
};

// Load both export users and customer data
const loadExportData = async () => {
  try {
    isLoadingCustomers.value = true;

    // Load export users for editing and cache locally
    await integrationStore.fetchIntegrations();
    cachedExportUsers.value = [...integrationStore.integrations];

    // Load customer data from export services and cache locally
    const customerData =
      await integrationStore.getCustomerListFromExportServices();

    if (customerData && customerData.customers) {
      cachedCustomers.value = [...customerData.customers];
      availableCustomers.value = [...customerData.customers];
    } else {
      cachedCustomers.value = [];
      availableCustomers.value = [];
      showNotification('No customers found from export service', {
        title: 'Export Associations',
        type: HealthLevel.WARNING,
      });
    }
  } catch (error) {
    console.error('Error fetching export data:', error);
    showNotification('Failed to fetch export data', {
      title: 'Export Associations',
      type: HealthLevel.ERROR,
    });
    // Initialize empty arrays on error
    cachedExportUsers.value = [];
    cachedCustomers.value = [];
    availableCustomers.value = [];
  } finally {
    isLoadingCustomers.value = false;
  }
};

const selectCustomer = (customer: CustomerData) => {
  selectedCustomer.value = customer;
  activeTab.value = 1; // Switch to details tab
};

// Association management methods
const addNewAssociation = async () => {
  if (!selectedCustomer.value || !selectedExportUser.value) {
    return;
  }

  // Initialize exportAssociations as empty array if null/undefined
  if (!props.clientDetails.exportAssociations) {
    props.clientDetails.exportAssociations = [];
  }

  const newAssociation: ExportIntegrations = {
    uniqueApiReference: selectedExportUser.value,
    remoteEntityReference: selectedCustomer.value.id,
  };

  // Replace any existing association (one association per client rule)
  props.clientDetails.exportAssociations = [newAssociation];

  // Close dialog first
  closeAddAssociationDialog();

  // Automatically save to backend
  try {
    const result = await props.clientDetails.save();
    if (result) {
      showNotification('Export association added successfully.', {
        title: 'Export Associations',
        type: HealthLevel.SUCCESS,
      });
    } else {
      showNotification('Failed to save changes to backend.', {
        title: 'Export Associations',
        type: HealthLevel.ERROR,
      });
    }
  } catch (error) {
    console.error('Error saving client details:', error);
    showNotification('Failed to save changes to backend.', {
      title: 'Export Associations',
      type: HealthLevel.ERROR,
    });
  }
};

// $emit from api_user_select. Contains the clients associated api username list
const setClientsApiUsers = (usernames: string[]) => {
  clientsApiUsers.value = usernames;
};

onMounted(() => {
  loadExportData();
});
</script>

<style scoped lang="scss">
.tab-item {
  background-color: var(--background-color--300);
}
</style>
