<template>
  <div class="business-central-customer-search">
    <!-- Customer search autocomplete -->
    <v-combobox
      v-model="selectedValue"
      :items="filteredCustomers"
      :loading="isLoading"
      :disabled="props.disabled"
      item-text="name"
      item-value="id"
      return-object
      solo
      flat
      clearable
      class="v-solo-custom"
      persistent-hint
      :rules="[validate.required]"
      @input="onValueChanged"
      @blur="onSearchBlur"
      hide-no-data
    >
      <template v-slot:prepend-item>
        <div v-if="!selectedValue" class="px-3 py-2">
          <span class="menu-head"> Business Central customers: </span>
        </div>
      </template>

      <template v-slot:item="{ item }">
        <div class="customer-item">
          <div class="customer-name">{{ item.name }}</div>
          <div class="customer-location text-caption text--secondary">
            {{ item.city }}, {{ item.state }}
          </div>
        </div>
      </template>
    </v-combobox>

    <!-- Data refresh info and button -->
    <v-flex class="refresh-section" v-if="!selectedValue">
      <span class="refresh-info ml-2">
        Business Central Customers Data last updated at:
        <span class="last-update-date"> {{ formattedLastUpdated }}</span>
      </span>
      <v-btn
        :loading="isRefreshing"
        @click="refreshCustomerData"
        :disabled="isRefreshing"
        icon
        small
      >
        <v-icon>refresh</v-icon>
      </v-btn>
    </v-flex>
  </div>
</template>

<script setup lang="ts">
import { returnFormattedTime } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { Validation } from '@/interface-models/Generic/Validation';
import { CustomerData } from '@/interface-models/InvoicingIntegration/CustomerDataSnapshot';
import { useIntegrationStore } from '@/store/modules/IntegrationStore';
import moment from 'moment-timezone';
import { computed, onMounted, ref, watch } from 'vue';

const props = withDefaults(
  defineProps<{
    disabled: boolean;
  }>(),
  {
    disabled: false,
  },
);

const emit = defineEmits<{
  (
    e: 'customerSelected',
    customer: CustomerData | null,
    searchText?: string,
  ): void;
}>();

const validate: Validation = validationRules;

const integrationStore = useIntegrationStore();

const selectedCustomer = ref<CustomerData | null>(null);
const selectedValue = ref<CustomerData | string | null>(null);
const customers = ref<CustomerData[]>([]);
const isLoading = ref(false);
const isRefreshing = ref(false);
const lastUpdated = ref<number>(0);

const hasCustomerPullCapability = computed(() => {
  return integrationStore.hasCustomerPullCapability;
});

const primaryExportUser = computed(() => {
  return integrationStore.getPrimaryCustomerPullExportUser;
});

const formattedLastUpdated = computed(() => {
  if (!lastUpdated.value) {
    return '';
  }
  return returnFormattedTime(lastUpdated.value);
});

const filteredCustomers = computed((): CustomerData[] => {
  return customers.value.filter((customers) => customers.name !== '');
});

/**
 * Loads customer data from Business Central export services.
 * Only executes if the integration has customer pull capability and a primary export user exists.
 * Updates the customers list and last updated timestamp on successful load.
 * Shows appropriate notifications for success/failure scenarios.
 */
async function loadCustomerData() {
  if (!hasCustomerPullCapability.value || !primaryExportUser.value?.id) {
    return;
  }

  try {
    isLoading.value = true;
    const response = await integrationStore.getCustomerListFromExportServices();

    if (response) {
      customers.value = response.customers || [];
      lastUpdated.value = Date.now();
    } else {
      customers.value = [];
      showNotification('Failed to load customer data from Business Central', {
        type: HealthLevel.WARNING,
      });
    }
  } catch (error) {
    console.error('Error loading customer data:', error);
    showNotification('Error loading customer data from Business Central', {
      type: HealthLevel.ERROR,
    });
  } finally {
    isLoading.value = false;
  }
}

/**
 * Refreshes customer data from Business Central by fetching the latest information.
 * Updates the last updated timestamp and reloads customer data after successful refresh.
 * Shows success/failure notifications to inform the user of the operation status.
 */
async function refreshCustomerData() {
  if (!hasCustomerPullCapability.value || !primaryExportUser.value?.id) {
    return;
  }

  try {
    isRefreshing.value = true;
    const response =
      // await integrationStore.refreshCustomerListFromExportServices();
      await integrationStore.getCustomerListFromExportServices();

    if (response) {
      lastUpdated.value = moment.now();
      // Reload the customer data after refresh
      await loadCustomerData();
      showNotification('Customer data refreshed successfully', {
        type: HealthLevel.SUCCESS,
      });
    } else {
      showNotification('Failed to refresh customer data', {
        type: HealthLevel.WARNING,
      });
    }
  } catch (error) {
    console.error('Error refreshing customer data:', error);
    showNotification('Error refreshing customer data from Business Central', {
      type: HealthLevel.ERROR,
    });
  } finally {
    isRefreshing.value = false;
  }
}

/**
 * Handles changes to the customer selection value in the combobox.
 * Distinguishes between customer object selection, text input, and cleared values.
 * Emits appropriate customerSelected events based on the value type.
 *
 * @param {CustomerData | string | null} value - The new value from the combobox
 */
function onValueChanged(value: CustomerData | string | null) {
  if (typeof value === 'object' && value !== null) {
    // User selected a customer from the dropdown
    selectedCustomer.value = value;
    emit('customerSelected', value, '');
  } else if (typeof value === 'string') {
    // User typed text without selecting a customer
    // This includes the case where user had a customer selected and then started typing
    selectedCustomer.value = null;
    emit('customerSelected', null, value);
  } else {
    // Value was cleared (null or undefined)
    selectedCustomer.value = null;
    emit('customerSelected', null, '');
  }
}

/**
 * Handles blur events on the search combobox.
 * Ensures that typed text without customer selection is properly emitted
 * when the user leaves the input field.
 */
function onSearchBlur() {
  // v-combobox preserves the text, handle the emit
  if (typeof selectedValue.value === 'string' && selectedValue.value.trim()) {
    // User typed text but didn't select a customer
    emit('customerSelected', null, selectedValue.value.trim());
  }
}

watch(
  () => integrationStore.integrations,
  () => {
    if (hasCustomerPullCapability.value && customers.value.length === 0) {
      loadCustomerData();
    }
  },
);

// Watch for changes in selectedValue to ensure we catch all user interactions
watch(selectedValue, (newValue, oldValue) => {
  // If user had a customer selected (object) and now has typed text (string)
  if (
    typeof oldValue === 'object' &&
    oldValue !== null &&
    typeof newValue === 'string' &&
    newValue.trim() !== ''
  ) {
    // User started typing after having a customer selected
    if (selectedCustomer.value !== null) {
      selectedCustomer.value = null;
      emit('customerSelected', null, newValue);
    }
  }
});

onMounted(async () => {
  // Ensure integrations are loaded
  if (integrationStore.integrations.length === 0) {
    await integrationStore.fetchIntegrations();
  }

  if (hasCustomerPullCapability.value) {
    await loadCustomerData();
  }
});
</script>

<style scoped lang="scss">
.menu-head {
  color: $warning;
}

.customer-item {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  .customer-name {
    font-weight: 500;
    .customer-location {
      color: #666;
    }
  }
}

.refresh-section {
  display: flex;
  justify-content: right;
  transform: translate(0, -30px);

  .refresh-info {
    margin-top: 12px;
    padding-right: 12px;
    color: var(--light-text-color);

    .last-update-date {
      color: $warning;
      font-weight: 700;
    }
  }

  .v-btn {
    color: black;
    background-color: $warning;
    border-radius: 50px;
    min-width: 38px;
    max-height: 24px;
  }
}
</style>
