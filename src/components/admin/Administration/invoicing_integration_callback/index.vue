<script>
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import GetTokensFromOauth2AuthCodeRequest from '@/interface-models/InvoicingIntegration/GetTokensFromOauth2AuthCodeRequest';

export default {
  data() {
    return {};
  },
  methods: {
    qb_exchange_code_for_tokens() {
      console.log('Sending request to QB for tokens');
      const getTokensFromOauth2AuthCodePayload =
        new GetTokensFromOauth2AuthCodeRequest(
          this.$route.query.code.toString(),
          this.bcTokenId,
          this.bcEnvironment,
        );

      /* console.log(getTokensFromOauth2AuthCodePayload);
      console.log(
        'Data type of overall payload: ' +
          typeof getTokensFromOauth2AuthCodePayload,
      );
      console.log(
        "Original data type of 'code': " + typeof this.$route.query.code,
      );
      console.log(
        "Data type of stringified 'code': " +
          typeof this.$route.query.code.toString(),
      ); */

      // Define POST requet to QB to obtain tokens...
      const auth_code = this.$route.query.code;
      const qb_token_link =
        'https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer?grant_type=authorization_code&code=' +
        auth_code +
        '&redirect_uri=http%3A%2F%2Flocalhost%3A8082%2Fadministration%2Finvoicing_integration_callback';

      const qb_token_request_payload = '';
      const godesta_client_id =
        'ABfMT3Pq1LiUhPz2Vuiz5tBRQ4OgqbBkUXbIk2PeAg4hiqhC08';
      const godesta_client_secret = 'FXvWyhhWojBaTjrsy7AJiuedyc2nEcsJzZLiH10o';
      const qb_basicauthtoken =
        'Basic ' + btoa(godesta_client_id + ':' + godesta_client_secret);
      const qb_headers = new Headers();
      qb_headers.append('Content-Type', 'application/x-www-form-urlencoded');
      qb_headers.append('Accept', 'application/json');
      qb_headers.append('Authorization', qb_basicauthtoken);
      const request = new Request(qb_token_link, {
        method: 'POST',
        headers: qb_headers,
        //mode: 'cors', // cors is the default
        //cache: 'default',
        //body: qb_token_request_payload,
      });

      console.log('Request: ');
      console.log(
        'Method: ' +
          request.method +
          "\nHeaders: '" +
          request.headers.get('Content-Type') +
          "'\n'" +
          request.headers.get('Accept') +
          "'\n Authorization: '" +
          request.headers.get('Authorization') +
          "'\nMode: " +
          request.mode +
          '\nCache: ' +
          request.cache,
      );

      fetch(request).then((response) => {
        if (response.status === 200) {
          console.log('Received tokens from QB: ' + response.json());
          return response.json();
        } else {
          console.log(
            'Error connecting to Quickbooks to obtain token: ' +
              response.status +
              '-' +
              response.statusText +
              ' ' +
              response.headers.get('Access-Control-Allow-Origin') +
              '\n\n' +
              JSON.stringify(response.body),
          );
        }
      });

      /*          try {
          // Send request over websocket
            console.log("\n\n\n\n\nSending WS request to for /export/saveBCCredentials\n\n\n");
            console.log(getTokensFromOauth2AuthCodePayload);
            const result = sendRequestAndListenForResponse(
              new WebSocketRequest(
                '/export/saveBCCredentials',
                getTokensFromOauth2AuthCodePayload,
                true,
              ),
              'getTokensFromOauth2AuthCodeRequest',
            );

            return result;
          } catch (error) {
              console.error('Error sending websocket request to backend', error);
              return null;
          } */

      //}
    },
  },
};
</script>

<template>
  <v-layout wrap class="reports-container px-3">
    <div class="top-panel">
      <v-spacer></v-spacer>
    </div>
    <v-flex
      md12
      class="app-bgcolor--400 app-borderside--a-2x app-bordercolor--600 mb-3"
    >
      <div class="pa-2 header-text">Accounting Integration</div>
      <v-divider class="ma-0 pa-0" />
      <v-form class="pa-3">
        <div
          v-if="$route.query.code && $route.query.state && $route.query.realmId"
        >
          <table>
            <tr>
              <td>Name</td>
              <td>Value</td>
            </tr>
            <tr>
              <td>code</td>
              <td>{{ $route.query.code }}</td>
            </tr>
            <tr>
              <td>state</td>
              <td>{{ $route.query.state }}</td>
            </tr>
            <tr>
              <td>realmId</td>
              <td>{{ $route.query.realmId }}</td>
            </tr>
          </table>
        </div>
        <div v-if="!$route.query.code">No authcode was received.</div>

        <div class="connect-to-provider">
          <i class="fal fa-key"></i>
          <button @click="qb_exchange_code_for_tokens">
            Exchange QB auth code for tokens
          </button>
        </div>
      </v-form>
    </v-flex>
  </v-layout>
</template>

<style>
.greeting {
  color: red;
  font-weight: bold;
}
.connect-to-provider {
  font-size: large;
  i {
    color: red;
    padding: 10px;
  }
}
</style>
