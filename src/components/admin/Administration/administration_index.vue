<template>
  <section id="administration-index-page" class="administration-index-page">
    <v-layout>
      <v-flex md3 class="admin-navigation-panel">
        <div class="left-panel">
          <v-toolbar dark flat dense class="header-bar">
            <h2>{{ companyDivision }} Administration</h2>
          </v-toolbar>
          <div class="scrollable">
            <v-list class="nav-list" dense>
              <v-layout v-for="item in selectOptions" :key="item.id">
                <v-flex md12 v-if="item.children.length === 0">
                  <v-list-tile
                    :disabled="item.disabled"
                    @click="selectView(item.id)"
                    class="nav-list-item"
                    :class="{ 'is-active': selectedView === item.id }"
                  >
                    <v-list-tile-action>
                      <v-icon
                        class="nav-list-item__icon"
                        v-if="item.icon"
                        size="16"
                        >{{ item.icon }}</v-icon
                      >
                    </v-list-tile-action>
                    <v-list-tile-content>
                      <v-list-tile-title>{{ item.title }}</v-list-tile-title>
                    </v-list-tile-content>
                  </v-list-tile>
                </v-flex>
                <v-flex md12 v-else>
                  <v-subheader style="height: 22px">
                    <span class="nav-title"> {{ item.title }}</span>
                  </v-subheader>
                  <v-list class="nav-list">
                    <v-list-tile
                      v-for="subItem in item.children"
                      :key="subItem.id"
                      @click="selectView(subItem.id)"
                      class="nav-list-item"
                      :class="{ 'is-active': selectedView === subItem.id }"
                    >
                      <v-list-tile-action v-if="$vuetify.breakpoint.lgAndUp">
                        <v-icon
                          class="nav-list-item__icon"
                          v-if="subItem.icon"
                          size="16"
                          >{{ subItem.icon }}</v-icon
                        >
                      </v-list-tile-action>
                      <v-list-tile-content>
                        <v-list-tile-title>
                          <span>{{ subItem.title }}</span>
                        </v-list-tile-title>
                      </v-list-tile-content>
                      <v-list-tile-action> </v-list-tile-action>
                    </v-list-tile>
                  </v-list>
                </v-flex>
              </v-layout>
            </v-list>
          </div>
        </div>
      </v-flex>
      <v-flex md9 class="middle-section">
        <v-layout class="middle-scrollable">
          <v-flex md12 class="pa-0">
            <v-flex v-if="selectedView === 'DOP'">
              <DivisionOperations />
            </v-flex>
            <v-flex v-if="selectedView === 'SRD'">
              <div class="client-default-rates">
                <DivisionServiceRateAdministration
                  :isDefault="true"
                  entityId="0"
                  :type="RateEntityType.CLIENT"
                  :key="'clientRateDefault'"
                />
              </div>
            </v-flex>
            <v-flex v-if="selectedView === 'CSR'">
              <div class="client-default-rates">
                <DivisionServiceRateAdministration
                  :isDefault="true"
                  entityId="CS"
                  :key="'cashSalesDefault'"
                />
              </div>
            </v-flex>
            <!-- FLEET ASSET Service Rates -->
            <v-flex v-if="selectedView === 'FSRD'">
              <div class="client-default-rates">
                <DivisionServiceRateAdministration
                  :isDefault="true"
                  entityId="0"
                  :type="RateEntityType.FLEET_ASSET"
                  :key="'fleetAssetRateDefault'"
                />
              </div>
            </v-flex>
            <v-flex
              px-3
              v-if="selectedView === 'CFS'"
              class="fuel-levy-container"
            >
              <FuelSurchargeLevyIndex
                entityId="CS"
                key="cashSaleFuelSurcharge"
              />
            </v-flex>

            <v-flex
              px-3
              v-if="selectedView === 'FSL'"
              class="fuel-levy-container"
            >
              <FuelSurchargeLevyIndex
                :type="RateEntityType.CLIENT"
                entityId="0"
                key="divisionFuelSurcharge"
              />
            </v-flex>
            <!-- FLEET ASSET FUEL SURCHARGE LEVY -->
            <v-flex
              px-3
              v-if="selectedView === 'FAFSL'"
              class="fuel-levy-container"
            >
              <FuelSurchargeLevyIndex
                :type="RateEntityType.FLEET_ASSET"
                entityId="0"
                key="fleetAssetFuelSurcharge"
              />
            </v-flex>
            <CompanyUserManagement v-if="selectedView === 'UAM'">
            </CompanyUserManagement>
            <v-layout v-if="selectedView === 'ADC'">
              <v-flex md12>
                <AdditionalChargesIndex />
              </v-flex>
            </v-layout>
            <v-layout v-if="selectedView === 'ADCC'">
              <v-flex md12>
                <AdditionalChargesCategoryIndex />
              </v-flex>
            </v-layout>
            <v-layout v-if="selectedView === 'AJC'">
              <v-flex md12>
                <AdjustmentCharges />
              </v-flex>
            </v-layout>
            <v-layout v-if="selectedView === 'IMS'">
              <v-flex md12>
                <InsideMetroSuburbs />
              </v-flex>
            </v-layout>
            <v-layout v-if="selectedView === 'IINT'">
              <v-flex md12>
                <InvoicingIntegration />
              </v-flex>
            </v-layout>
            <v-layout v-if="selectedView === 'SCHR'">
              <v-flex md12>
                <RecurringJobSchedulerMaintenance />
              </v-flex>
            </v-layout>
            <v-layout v-if="selectedView === 'PERM'">
              <v-flex md12>
                <RecurringJobList />
              </v-flex>
            </v-layout>

            <v-layout v-if="selectedView === 'ARM'">
              <v-flex md12>
                <AccountRecoveryManagement />
              </v-flex>
            </v-layout>

            <v-layout v-if="selectedView === 'AUD' && isAuthorised()">
              <v-flex md12>
                <AuditHistoryComponent />
              </v-flex>
            </v-layout>
            <v-layout
              v-if="selectedView === 'SAM' && isAuthorisedSalesManagement()"
            >
              <v-flex md12>
                <SalesManagement />
              </v-flex>
            </v-layout>
            <v-layout v-if="selectedView === 'NCID'">
              <v-flex md12 class="px-3">
                <NationalClientIdentifier />
              </v-flex>
            </v-layout>

            <v-layout v-if="selectedView === 'REP'">
              <ReportIndex />
            </v-layout>
            <SmsMessageHistory
              v-if="selectedView === 'SMS'"
            ></SmsMessageHistory>
            <EmailMessageHistory v-if="selectedView === 'EML'">
            </EmailMessageHistory>
            <SafetyChecklistHistory v-if="selectedView === 'CHK'">
            </SafetyChecklistHistory>
            <ServiceTypesAdministrationIndex v-if="selectedView === 'SERV'">
            </ServiceTypesAdministrationIndex>
            <DriverComplianceFormIndex
              v-if="selectedView === 'DCF'"
            ></DriverComplianceFormIndex>

            <v-layout v-if="selectedView === 'CD'">
              <v-flex lg8 md10 offset-lg2 offset-md1 class="pt-3">
                <CompanyDetails />
              </v-flex>
            </v-layout>

            <v-layout v-if="selectedView === 'HD'">
              <v-flex md12 class="pa-4">
                <HolidayDetailsList />
              </v-flex>
            </v-layout>

            <v-layout v-if="selectedView === 'ID'">
              <v-flex md12 class="pa-4">
                <InductionTypesList />
              </v-flex>
            </v-layout>

            <v-layout v-if="selectedView === 'CCA'">
              <v-flex md12 class="pa-4">
                <CommonAddressList />
              </v-flex>
            </v-layout>

            <v-layout v-if="selectedView === 'JQD'">
              <v-flex md12 class="pa-4">
                <QuoteDetailsList />
              </v-flex>
            </v-layout>

            <v-layout v-if="selectedView === 'RT'">
              <v-flex md12 class="pa-4">
                <RateTypesList />
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
import AccountRecoveryManagement from '@/components/admin/Administration/account-recovery-management/account-recovery-management.vue';
import AdditionalChargesCategoryIndex from '@/components/admin/Administration/additional_charges_category_index/additional_charges_category_index.vue';
import AdditionalChargesIndex from '@/components/admin/Administration/additional_charges_index/additional_charges_index.vue';
import AdjustmentCharges from '@/components/admin/Administration/adjustment-charges/adjustment_charges.vue';
import AuditHistoryComponent from '@/components/admin/Administration/audit_history/audit_history.vue';
import CommonAddressList from '@/components/admin/Administration/common_address_list/common_address_list.vue';
import CompanyDetails from '@/components/admin/Administration/company_details/company_details.vue';
import CompanyUserManagement from '@/components/admin/Administration/company_user_management/company_user_management.vue';
import DivisionOperations from '@/components/admin/Administration/division_operations/division_operations.vue';
import DivisionServiceRateAdministration from '@/components/admin/Administration/division_service_rate_administration/division_service_rate_administration.vue';
import DriverComplianceFormIndex from '@/components/admin/Administration/driver_compliance_form/driver_compliance_form_index.vue';
import EmailMessageHistory from '@/components/admin/Administration/email_message_history/email_message_history.vue';
import FuelSurchargeLevyIndex from '@/components/admin/Administration/fuel-surcharge-levy-index/fuel_surcharge_levy_index.vue';
import HolidayDetailsList from '@/components/admin/Administration/holiday_details/holiday_details_list.vue';
import InductionTypesList from '@/components/admin/Administration/induction_types_list/induction_types_list.vue';
import InsideMetroSuburbs from '@/components/admin/Administration/inside_metro_suburbs/inside_metro_suburbs.vue';
import InvoicingIntegration from '@/components/admin/Administration/invoicing_integration/invoicing_integration_index.vue';
import NationalClientIdentifier from '@/components/admin/Administration/national_client_identifier/national_client_identifier.vue';
import QuoteDetailsList from '@/components/admin/Administration/quote_details_list/quote_details_list.vue';
import RateTypesList from '@/components/admin/Administration/rate_types_list/rate_types_list.vue';
import RecurringJobList from '@/components/admin/Administration/recurring_jobs/recurring_job_list.vue';
import RecurringJobSchedulerMaintenance from '@/components/admin/Administration/recurring_jobs/recurring_job_scheduler_maintenance.vue';
import ReportIndex from '@/components/admin/Administration/reporting/report_index/report_index.vue';
import SafetyChecklistHistory from '@/components/admin/Administration/safety_checklist_history/safety_checklist_history.vue';
import SalesManagement from '@/components/admin/Administration/sales_management/sales_management.vue';
import ServiceTypesAdministrationIndex from '@/components/admin/Administration/service_types_administration/service_types_administration_index.vue';
import SmsMessageHistory from '@/components/admin/Administration/sms_message_history/sms_message_history.vue';
import {
  hasAdminOrHeadOfficeOrBranchManagerRole,
  hasAdminOrHeadOfficeRole,
} from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { sessionManager } from '@/store/session/SessionState';
import { ComputedRef, Ref, computed, ref } from 'vue';

const selectedView: Ref<string> = ref('SRD');

const selectOptions = ref([
  {
    id: 'RATES',
    title: 'Client Default Rates',
    icon: 'fal fa-dollar-sign',
    children: [
      {
        id: 'SRD',
        title: 'Client Rates',
        icon: 'fal fa-dollar-sign',
        children: [],
        disabled: false,
      },
      {
        id: 'FSL',
        title: 'Client Fuel Surcharge Levy',
        icon: 'fal fa-gas-pump',
        children: [],
        disabled: false,
      },
      {
        id: 'CSR',
        title: 'Cash Sale Rates',
        icon: 'fal fa-money-bill-alt',
        children: [],
        disabled: false,
      },
      {
        id: 'CFS',
        title: 'Cash Sale Fuel Surcharge Levy',
        icon: 'fal fa-gas-pump',
        children: [],
        disabled: false,
      },
    ],
    disabled: false,
  },
  {
    id: 'FLEETRATES',
    title: 'Fleet Asset Default Rates',
    icon: 'fal fa-dollar-sign',
    children: [
      {
        id: 'FSRD',
        title: 'Fleet Rates',
        icon: 'fal fa-dollar-sign',
        children: [],
        disabled: false,
      },
      {
        id: 'FAFSL',
        title: 'Fleet Fuel Surcharge Levy',
        icon: 'fal fa-gas-pump',
        children: [],
        disabled: false,
      },
    ],
    disabled: false,
  },
  {
    id: 'REPADMIN',
    title: 'Reporting and Administration',
    icon: '',
    children: [
      {
        id: 'REP',
        title: 'Reporting',
        icon: 'fal fa-chart-bar',
        children: [],
        disabled: false,
      },
      {
        id: 'SAM',
        title: 'Sales Management',
        icon: 'fal fa-piggy-bank',
        children: [],
        disabled: !isAuthorisedSalesManagement(),
      },
      {
        id: 'NCID',
        title: 'National Client Identification',
        icon: 'fal fa-fingerprint',
        children: [],
        disabled: false,
      },
      {
        id: 'CCA',
        title: 'Client Common Address',
        icon: 'fal fa-address-card',
        children: [],
        disabled: false,
      },
      {
        id: 'UAM',
        title: 'User Account Management',
        icon: 'fal fa-user-circle',
        children: [],
        disabled: false,
      },
      {
        id: 'ARM',
        title: 'Account Recovery Management',
        icon: 'fal fa-user-circle',
        children: [],
        disabled: false,
      },
      {
        id: 'AJC',
        title: 'Adjustment Charges',
        icon: 'fal fa-file-chart-line',
        children: [],
        disabled: false,
      },
      {
        id: 'AUD',
        title: 'Audit History',
        icon: 'fal fa-pencil',
        children: [],
        disabled: !isAuthorised(),
      },
    ],
    disabled: false,
  },
  {
    id: 'AC',
    title: 'Additional Charges',
    icon: '',
    children: [
      {
        id: 'ADC',
        title: 'Additional Charges',
        icon: 'fal fa-receipt',
        children: [],
        disabled: false,
      },
      {
        id: 'ADCC',
        title: 'Additional Charges Category',
        icon: 'category',
        children: [],
        disabled: false,
      },
    ],
    disabled: false,
  },
  {
    id: 'OPS',
    title: 'Operations and Jobs',
    icon: 'fal fa-briefcase',
    children: [
      {
        id: 'SERV',
        title: 'Service Types',
        icon: 'fal fa-th-list',
        children: [],
        disabled: false,
      },
      {
        id: 'RT',
        title: 'Rate Types',
        icon: 'fal fa-money-bill',
        children: [],
        disabled: false,
      },
      {
        id: 'JQD',
        title: 'Quote History',
        icon: 'fal fa-quote-right',
        children: [],
        disabled: false,
      },
      {
        id: 'DOP',
        title: 'Default Load Durations',
        icon: 'fal fa-briefcase',
        children: [],
        disabled: false,
      },
      {
        id: 'IMS',
        title: 'Inside Metro Suburbs',
        icon: 'fal fa-location-circle',
        children: [],
        disabled: false,
      },
    ],
    disabled: false,
  },
  {
    id: 'INTEG',
    title: 'Invoicing Integration',
    icon: '',
    children: [
      {
        id: 'IINT',
        title: 'Invoicing Integration',
        icon: 'fal fa-link',
        children: [],
        disabled: false,
      },
    ],
    disabled: false,
  },
  {
    id: 'RJS',
    title: 'Permanent Jobs',
    icon: 'fal fa-calendar-alt',
    children: [
      {
        id: 'SCHR',
        icon: 'fal fa-gas-pump',
        title: 'Scheduler Planned Runs',
      },
      {
        id: 'PERM',
        icon: 'fal fa-calendar-alt',
        title: 'Current Permanent Jobs',
      },
    ],
    disabled: false,
  },
  {
    id: 'MSG',
    title: 'SMS and Email Logs',
    icon: 'fal fa-mail-bulk',
    children: [
      { id: 'SMS', icon: 'fal fa-sms', title: 'SMS Message History' },
      { id: 'EML', icon: 'fal fa-inbox-out', title: 'Email History' },
    ],
    disabled: false,
  },
  {
    id: 'COMP',
    title: 'Compliance',
    icon: 'fal fa-clipboard-list-check',
    children: [
      {
        id: 'DCF',
        icon: 'checklist_rtl',
        title: 'Compliance Forms',
      },
      {
        id: 'CHK',
        icon: 'fal fa-clipboard-list-check',
        title: 'Driver Checklist History',
      },
      {
        id: 'ID',
        title: 'Induction Types',
        icon: 'fal fa-flag-checkered',
        children: [],
        disabled: false,
      },
    ],
    disabled: false,
  },
  {
    id: 'COMPANYDETAILS',
    title: 'Company Details',
    icon: 'fal fa-dollar-sign',
    children: [
      {
        id: 'CD',
        title: 'Company Details',
        icon: 'fal fa-info-circle',
        children: [],
        disabled: false,
      },
      {
        id: 'HD',
        title: 'Holiday Details',
        icon: 'fal fa-calendar',
        children: [],
        disabled: false,
      },
    ],
    disabled: false,
  },
]);

/**
 * Returns a string representing the company and division, to be displayed in
 * the template.
 */
const companyDivision: ComputedRef<string> = computed(() => {
  return sessionManager.getCompanyId() + ' - ' + sessionManager.getDivisionId();
});

/**
 * Sets the current view to the provided id.
 * @param {string} id - The ID of the view to select.
 */
function selectView(id: string) {
  selectedView.value = id;
}

/**
 * Checks if the user is authorized for certain actions. For use in the template
 * to enable or disable certain views.
 * @returns {boolean} True if the user has admin or head office role, false
 * otherwise.
 */
function isAuthorised(): boolean {
  return hasAdminOrHeadOfficeRole();
}

/**
 * Checks if the user is authorized for certain actions. For use in the template
 * to enable or disable certain views.
 * @returns {boolean} True if the user has admin, head office, or branch manager
 * role, false otherwise.
 */
function isAuthorisedSalesManagement(): boolean {
  return hasAdminOrHeadOfficeOrBranchManagerRole();
}
</script>

<style scoped lang="scss">
.administration-index-page {
  background-color: var(--background-color-250) !important;
}

.admin-navigation-panel {
  .left-panel {
    position: fixed;
    transition: 0.2s;
    height: calc(100vh - 40px);
    width: calc(25% - 14px);
    z-index: 2;
    background-color: var(--background-color-250) !important;
    border-right: 1px solid $translucent;
  }
}

.header-bar {
  background-color: var(--background-color-400) !important;
  border-bottom: 1px solid $translucent;
  z-index: 9;
}

.nav-title {
  color: var(--bg-light);
  font-size: $font-size-12;
  font-weight: 700;
  text-transform: uppercase;
  padding: 4px;
}

.nav-title {
  color: var(--bg-light);
  font-size: $font-size-12;
  font-weight: 700;
  text-transform: uppercase;
  padding: 4px;
}

.nav-list {
  background: none !important;
  .nav-list-item {
    color: var(--text-color);
    background: none;
    transition: 0s;
    .v-list__tile__title {
      transition: 0s;
    }
    .nav-list-item__icon {
      color: var(--primary);
    }

    &.is-active {
      background-color: $bg-light;
      border-left: 5px solid var(--primary);
      color: black;

      span {
        font-size: $font-size-15;
        font-weight: 500;
      }
      .nav-list-item__icon {
        color: var(--primary);
        font-weight: 600;
      }
    }
  }
}

.middle-section {
  height: calc(100vh - 44px);
  background-color: $app-dark-primary-250 !important;

  .middle-scrollable {
    height: calc(100vh - 44px);
    max-height: calc(100vh);
    overflow-y: scroll;
    overscroll-behavior-y: contain;
    background-color: var(--background-color-250) !important;
  }
}

.scrollable {
  background-color: var(--background-color-250) !important;
  height: calc(100vh - 88px);
  max-height: calc(100vh - 88px);
  overflow-y: scroll;
  overscroll-behavior-y: contain;
}
</style>
