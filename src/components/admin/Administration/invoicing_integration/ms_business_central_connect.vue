<template>
  <div
    class="ms-bc-connect-card"
    :class="tokenStatus === 'success' ? 'connected' : ''"
  >
    <div class="header-left" v-if="tokenStatus !== 'success'">
      <h3 class="subheader--light">Microsoft Business Central Connection</h3>
      <div class="status-chips">
        <v-chip :color="msStatusInfo.color" small class="mr-2">
          {{ msStatusInfo.text }}
        </v-chip>
        <v-chip :color="tokenStatusInfo.color" small>{{
          tokenStatusInfo.text
        }}</v-chip>
      </div>
    </div>
    <div v-if="!props.isEdit" class="info-grid">
      <div class="info-item">
        <span class="info-label">Tenant ID</span>
        <span class="info-value">{{ bcTokenId || '—' }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Environment</span>
        <span class="info-value">{{ bcEnvironment || '—' }}</span>
      </div>
      <div class="info-item" v-if="tokenStatus !== 'success'">
        <span class="info-label">Last Used URL</span>
        <span class="info-value last-url">{{ lastUsedBcUrl || '—' }}</span>
      </div>
      <div class="connected-info-item" v-else>
        <v-flex class="connected-info-label"
          >Connected to Microsoft Business Central
          <i class="fad fa-check-double pt-1 pl-2"></i>
        </v-flex>
      </div>
    </div>
    <div
      v-if="!(msStatus === 'received' && tokenStatus === 'success')"
      class="form-row"
    >
      <v-text-field
        v-if="!props.isEdit"
        v-model="inputBcUrl"
        label="Business Central URL"
        hint="Business Central URL"
        :placeholder="lastUsedBcUrl"
        dense
        hide-details
      >
        <template #append>
          <v-btn
            icon
            @click="pasteFromClipboard"
            :disabled="loading"
            class="paste-btn"
          >
            <v-icon size="20">content_paste</v-icon>
          </v-btn>
        </template>
      </v-text-field>

      <!-- Show warning when exportTypes are not available -->
      <div v-if="!isExportTypesAvailable" class="export-types-warning mb-3">
        <v-alert type="warning" dense outlined>
          <v-icon slot="prepend">warning</v-icon>
          Integration configuration is not available. Please contact your
          administrator to configure the export types.
        </v-alert>
      </div>

      <!-- Show detailed error messages based on status -->
      <div v-if="showDetailedErrorMessage" class="error-message mb-3">
        <v-alert type="error" dense outlined>
          <v-icon slot="prepend">error</v-icon>
          <div>
            <strong>{{ errorTitle }}</strong>
            <p class="mb-0 mt-1">{{ errorMessage }}</p>
            <div v-if="errorActions.length > 0" class="mt-2">
              <v-btn
                v-for="action in errorActions"
                :key="action.text"
                small
                outlined
                color="error"
                class="mr-2"
                @click="action.handler"
              >
                {{ action.text }}
              </v-btn>
            </div>
          </div>
        </v-alert>
      </div>

      <!-- Connect button - only show when exportTypes are available -->
      <v-btn
        v-if="isExportTypesAvailable"
        :loading="loading"
        color="primary"
        @click="startAuthFlow"
        :disabled="loading || !canStartAuthFlow"
        class="connect-btn"
        block
      >
        <v-icon left class="pr-2">key</v-icon>
        Connect
      </v-btn>

      <!-- Disabled connect button with tooltip when configuration is missing -->
      <v-tooltip v-else bottom>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            disabled
            color="grey"
            class="connect-btn"
            block
            v-bind="attrs"
            v-on="on"
          >
            <v-icon left class="pr-2">key</v-icon>
            Connect
          </v-btn>
        </template>
        <span>Integration configuration is required to connect</span>
      </v-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CreateExportUserResponse } from '@/interface-models/InvoicingIntegration/ExportUser';
import { useIntegrationStore } from '@/store/modules/IntegrationStore';
import {
  computed,
  defineEmits,
  defineProps,
  onBeforeUnmount,
  onMounted,
  Ref,
  ref,
} from 'vue';

const props = withDefaults(
  defineProps<{
    redirectUri: string;
    tenantId: string;
    isEdit?: boolean;
  }>(),
  {
    isEdit: false,
  },
);

const emit = defineEmits<{
  (e: 'connectionSuccessful', payload: CreateExportUserResponse): void;
}>();

const integrationStore = useIntegrationStore();

const loading: Ref<boolean> = ref(false);
const popupInterval: Ref<number | null> = ref(null);
const popup: Ref<Window | null> = ref(null);

const msStatus = ref<
  'not connected' | 'opened' | 'received' | 'failed' | 'cancelled'
>('not connected');
const tokenStatus = ref<
  'no token' | 'exchanging' | 'success' | 'failed' | 'permission_denied'
>('no token');

// Track authorization attempts to prevent rapid successive attempts
const lastAuthAttempt = ref<number>(0);
const authAttemptCooldown = 2000; // 2 seconds cooldown between attempts

// Track popup state more comprehensively
const popupState = ref<{
  isOpen: boolean;
  url: string | null;
  startTime: number | null;
}>({
  isOpen: false,
  url: null,
  startTime: null,
});

const inputBcUrl: Ref<string> = ref('');

const lastUsedBcUrl: Ref<string> = ref(
  'https://businesscentral.dynamics.com/c915ca25-3b9e-4765-9768-212473b32d30/Sandbox',
);

const exportTypes = computed(() => {
  return integrationStore.exportTypes;
});

// Check if exportTypes configuration is available and valid
const isExportTypesAvailable = computed(() => {
  const types = exportTypes.value;
  return (
    types !== null &&
    types !== undefined &&
    types.clientId &&
    types.authUrl &&
    types.tokenUrl &&
    types.authRequestAttributes
  );
});

// Check if all conditions are met to start the auth flow
const canStartAuthFlow = computed(() => {
  return isExportTypesAvailable.value && effectiveBcUrl.value && !loading.value;
});

// Error handling system
interface ErrorAction {
  text: string;
  handler: () => void;
}

// Computed properties for detailed error messages
const showDetailedErrorMessage = computed(() => {
  return (
    (msStatus.value === 'failed' ||
      tokenStatus.value === 'failed' ||
      tokenStatus.value === 'permission_denied') &&
    !loading.value
  );
});

const errorTitle = computed(() => {
  if (tokenStatus.value === 'permission_denied') {
    return 'Permission Denied';
  } else if (tokenStatus.value === 'failed') {
    return 'Token Exchange Failed';
  } else if (msStatus.value === 'failed') {
    return 'Authentication Failed';
  }
  return 'Connection Error';
});

const errorMessage = computed(() => {
  if (tokenStatus.value === 'permission_denied') {
    return 'The provided credentials do not have sufficient permissions to access Business Central. Please ensure your account has the necessary permissions or contact your administrator.';
  } else if (tokenStatus.value === 'failed') {
    return 'Failed to exchange the authorization code for access tokens. This may be due to an expired authorization code or server error.';
  } else if (msStatus.value === 'failed') {
    return 'Failed to complete the Microsoft authentication process. This could be due to popup blocking, network issues, or authentication server problems.';
  }
  return 'An unexpected error occurred during the connection process.';
});

const errorActions = computed((): ErrorAction[] => {
  const actions: ErrorAction[] = [];

  if (tokenStatus.value === 'permission_denied') {
    actions.push({
      text: 'Try Different Account',
      handler: () => {
        resetConnectionState();
      },
    });
  } else if (tokenStatus.value === 'failed' || msStatus.value === 'failed') {
    actions.push({
      text: 'Retry Connection',
      handler: () => {
        resetConnectionState();
      },
    });
  }

  return actions;
});

// Function to reset connection state for retry
function resetConnectionState() {
  msStatus.value = 'not connected';
  tokenStatus.value = 'no token';
  loading.value = false;

  // Clear any existing popup and intervals
  if (popup.value && !popup.value.closed) {
    popup.value.close();
  }
  if (popupInterval.value) {
    clearInterval(popupInterval.value);
    popupInterval.value = null;
  }

  popupState.value = {
    isOpen: false,
    url: null,
    startTime: null,
  };
}

const effectiveBcUrl = computed(
  () => inputBcUrl.value?.trim() || lastUsedBcUrl.value,
);

const bcTokenId = computed(() => {
  try {
    const url = new URL(effectiveBcUrl.value);
    const parts = url.pathname.split('/');
    return parts[1] || '';
  } catch {
    return '';
  }
});

const bcEnvironment = computed(() => {
  try {
    const url = new URL(effectiveBcUrl.value);
    const parts = url.pathname.split('/');
    return parts[2] || '';
  } catch {
    return '';
  }
});

// Status mappings for chips
const statusMappings = {
  ms: {
    opened: { text: 'Auth Window Opened', color: 'info' },
    received: { text: 'Auth Code Received', color: 'success' },
    failed: { text: 'Auth Failed', color: 'error' },
    cancelled: { text: 'Auth Cancelled', color: 'warning' },
    'not connected': { text: 'Not Connected', color: 'error' },
  },
  token: {
    exchanging: { text: 'Exchanging Token...', color: 'info' },
    success: { text: 'Token Success', color: 'success' },
    failed: { text: 'Token Failed', color: 'error' },
    permission_denied: { text: 'Permission Denied', color: 'error' },
    'no token': { text: 'No Token', color: 'none' },
  },
};

// Consolidated computed properties for status chips
const msStatusInfo = computed(() => {
  return (
    statusMappings.ms[msStatus.value] || statusMappings.ms['not connected']
  );
});

const tokenStatusInfo = computed(() => {
  return (
    statusMappings.token[tokenStatus.value] || statusMappings.token['no token']
  );
});

/**
 * Reads text from the system clipboard and sets it as the Business Central URL input value.
 * Handles clipboard access errors gracefully by logging them to console.
 */
async function pasteFromClipboard() {
  try {
    const text = await navigator.clipboard.readText();
    if (text) {
      inputBcUrl.value = text;
    }
  } catch (e) {
    console.log('Failed to read clipboard', e);
  }
}

/**
 * Constructs the OAuth2 authorization URL for Microsoft Business Central authentication.
 * Includes all required parameters for PKCE flow including client ID, redirect URI,
 * scope, and code challenge.
 *
 * @returns {string} The complete authorization URL with query parameters, or empty string if export types not available
 */
function buildAuthUrl(): string {
  if (!exportTypes.value) {
    return '';
  }
  const params = new URLSearchParams({
    redirect_uri: props.redirectUri,
    client_id: exportTypes.value.clientId,
    response_type: exportTypes.value.authRequestAttributes.response_type,
    response_mode: exportTypes.value.authRequestAttributes.response_mode,
    scope: exportTypes.value
      ? decodeURIComponent(exportTypes.value.authRequestAttributes.scope)
      : '',
    state: 'godesta_' + Math.random().toString(36).substring(2, 10),
    code_challenge: integrationStore.pkceChallenge,
    code_challenge_method: 'S256',
  });
  const authUrl = exportTypes.value.authUrl.replace(
    '${tenantId}',
    props.tenantId,
  );
  return `${authUrl}?${params.toString()}`;
}

/**
 * Opens a centered popup window for Microsoft Business Central authentication.
 * Calculates optimal positioning based on current window dimensions.
 * Includes enhanced state tracking and error handling.
 *
 * @param {string} url - The authorization URL to open in the popup
 */
function openPopup(url: string) {
  // Close any existing popup first
  if (popup.value && !popup.value.closed) {
    popup.value.close();
  }

  const width = 600;
  const height = 700;
  const left = window.screenX + (window.outerWidth - width) / 2;
  const top = window.screenY + (window.outerHeight - height) / 2;

  // Update popup state tracking
  popupState.value = {
    isOpen: true,
    url: url,
    startTime: Date.now(),
  };

  popup.value = window.open(
    url,
    'ms_bc_auth',
    `width=${width},height=${height},left=${left},top=${top}`,
  );

  // Handle popup blocking
  if (!popup.value) {
    msStatus.value = 'failed';
    loading.value = false;
    popupState.value.isOpen = false;
    console.error('Popup was blocked by browser');
    return;
  }

  // Handle immediate popup closure (e.g., user closes it immediately)
  setTimeout(() => {
    if (popup.value && popup.value.closed && msStatus.value === 'opened') {
      msStatus.value = 'cancelled';
      loading.value = false;
      popupState.value.isOpen = false;
    }
  }, 1000);
}

/**
 * Sets up polling to monitor the authentication popup for authorization code.
 * Checks popup status every second and handles successful authorization or popup closure.
 * Updates authentication status and triggers token exchange when code is received.
 * Enhanced with better error handling and timeout management.
 */
function listenForAuthCode() {
  const maxWaitTime = 300000; // 5 minutes timeout
  const startTime = Date.now();

  popupInterval.value = window.setInterval(async () => {
    try {
      // Check for timeout
      if (Date.now() - startTime > maxWaitTime) {
        if (popupInterval.value) {
          clearInterval(popupInterval.value);
        }
        if (popup.value && !popup.value.closed) {
          popup.value.close();
        }
        loading.value = false;
        msStatus.value = 'failed';
        popupState.value.isOpen = false;
        console.error('Authentication timeout after 5 minutes');
        return;
      }

      if (!popup.value || popup.value.closed) {
        if (popupInterval.value) {
          clearInterval(popupInterval.value);
        }
        loading.value = false;
        popupState.value.isOpen = false;

        // Distinguish between user cancellation and failure
        if (msStatus.value === 'opened') {
          msStatus.value = 'cancelled';
        } else if (msStatus.value !== 'received') {
          msStatus.value = 'failed';
        }
        return;
      }

      const popupUrl = popup.value.location.href;

      // Check for error parameters in the URL
      if (popupUrl.includes('error=')) {
        const urlObj = new URL(popupUrl);
        const error = urlObj.searchParams.get('error');
        const errorDescription = urlObj.searchParams.get('error_description');

        if (popupInterval.value) {
          clearInterval(popupInterval.value);
        }
        popup.value.close();
        popupState.value.isOpen = false;
        loading.value = false;

        if (error === 'access_denied') {
          msStatus.value = 'cancelled';
          tokenStatus.value = 'permission_denied';
        } else {
          msStatus.value = 'failed';
          tokenStatus.value = 'failed';
        }

        console.error('OAuth error:', error, errorDescription);
        return;
      }

      if (popupUrl.startsWith(props.redirectUri)) {
        const urlObj = new URL(popupUrl);
        const code = urlObj.searchParams.get('code');
        // const state = urlObj.searchParams.get('state');
        if (code) {
          if (popupInterval.value) {
            clearInterval(popupInterval.value);
          }
          popup.value.close();
          popupState.value.isOpen = false;
          msStatus.value = 'received';
          tokenStatus.value = 'exchanging';
          await handleTokenExchange(code);
        }
      }
    } catch (e) {
      // Ignore cross-origin errors until redirect
      // But log unexpected errors
      if (e instanceof Error && !e.message.includes('cross-origin')) {
        console.warn('Unexpected error in auth code listener:', e);
      }
    }
  }, 1000);
}

/**
 * Exchanges the OAuth2 authorization code for access and refresh tokens.
 * Creates export user via the integration store.
 * The createExportUser API handles token management internally, so no separate token update is needed.
 * Enhanced with comprehensive error handling and user feedback.
 *
 * @param {string} code - The authorization code received from Microsoft Business Central
 */
async function handleTokenExchange(code: string) {
  try {
    loading.value = true;
    tokenStatus.value = 'exchanging';

    // Use store to exchange code for tokens and create export user
    // The createExportUser API handles all token management internally
    const result = await integrationStore.exchangeCodeForTokens(
      code,
      bcTokenId.value,
      bcEnvironment.value,
    );

    if (result && result.companies) {
      // Export user created successfully with companies accessible
      tokenStatus.value = 'success';
      emit('connectionSuccessful', result);
    } else if (result && !result.companies) {
      // Token exchange succeeded but no companies found - permission issue
      tokenStatus.value = 'permission_denied';
      console.error(
        'Token exchange succeeded but no Business Central companies accessible',
      );
    } else {
      tokenStatus.value = 'failed';
      console.error('Token exchange failed - no result returned');
    }
  } catch (err) {
    tokenStatus.value = 'failed';
    console.error('Error during token exchange:', err);

    // Check for specific error types
    if (err instanceof Error) {
      if (
        err.message.includes('access_denied') ||
        err.message.includes('insufficient_scope')
      ) {
        tokenStatus.value = 'permission_denied';
      } else if (err.message.includes('invalid_grant')) {
        // Authorization code expired or invalid - user needs to try again
        msStatus.value = 'failed';
      }
    }
  } finally {
    loading.value = false;
  }
}

/**
 * Initiates the Microsoft Business Central OAuth2 authentication flow.
 * Generates PKCE challenge, builds authorization URL, opens popup, and starts monitoring.
 * Saves the used URL to localStorage for future reference.
 */
async function startAuthFlow() {
  // Check cooldown period to prevent rapid successive attempts
  const now = Date.now();
  if (now - lastAuthAttempt.value < authAttemptCooldown) {
    console.warn('Auth attempt too soon, please wait before trying again');
    return;
  }

  // Validate that we can start the auth flow
  if (!canStartAuthFlow.value) {
    console.error(
      'Cannot start auth flow: missing required configuration or URL',
    );
    return;
  }

  // Additional validation for exportTypes
  if (!isExportTypesAvailable.value) {
    console.error(
      'Cannot start auth flow: exportTypes configuration is not available',
    );
    msStatus.value = 'failed';
    return;
  }

  // Close any existing popup to prevent multiple popups
  if (popup.value && !popup.value.closed) {
    popup.value.close();
  }

  // Clear any existing intervals
  if (popupInterval.value) {
    clearInterval(popupInterval.value);
    popupInterval.value = null;
  }

  lastAuthAttempt.value = now;
  loading.value = true;
  msStatus.value = 'opened';
  tokenStatus.value = 'no token';

  try {
    await integrationStore.generatePkce();
    const urlToUse = effectiveBcUrl.value;
    if (!urlToUse) {
      loading.value = false;
      msStatus.value = 'failed';
      return;
    }

    const authUrl = buildAuthUrl();
    if (!authUrl) {
      loading.value = false;
      msStatus.value = 'failed';
      console.error('Failed to build authorization URL');
      return;
    }

    openPopup(authUrl);
    listenForAuthCode();
    // Save to localStorage for next time
    localStorage.setItem('msbc_last_used_url', urlToUse);
    lastUsedBcUrl.value = urlToUse;
  } catch (error) {
    loading.value = false;
    msStatus.value = 'failed';
    console.error('Error starting auth flow:', error);
  }
}

onMounted(() => {
  const last = localStorage.getItem('msbc_last_used_url');
  if (last) {
    lastUsedBcUrl.value = last;
  }
});

onBeforeUnmount(() => {
  if (popupInterval.value) {
    clearInterval(popupInterval.value);
  }
  if (popup.value && !popup.value.closed) {
    popup.value.close();
  }
});
</script>

<style lang="scss" scoped>
.ms-bc-connect-card {
  border-radius: 12px;
  border: 2px solid var(--border-color);
  padding: 12px 24px 0 24px;
  background: var(--background-color-300);
  transition: all 0.5s;

  &.connected {
    border-color: $toast-success-border;
  }

  .header-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 22px;
    margin-bottom: 12px;
    justify-content: space-between;
    .status-chips {
      display: flex;
      gap: 8px;
      margin-top: 4px;
    }
  }
  .info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 18px;
    .connected-info-item {
      padding: 12px 0;
      border-radius: 8px;
      background-color: var(--background-color-200);
      .connected-info-label {
        margin-left: 12px;
        display: flex;
        color: $success;
        font-weight: 700;
        font-size: 14px;
      }
    }
    .info-item {
      display: flex;
      flex-direction: column;
      gap: 2px;
      margin-left: 8px;
    }
    .info-label {
      font-size: 0.92em;
      color: var(--light-text-color);
      font-weight: 500;
    }
    .info-value {
      font-size: 1.05em;
      color: var(--text-color);
    }
    .last-url {
      font-size: 0.97em;
      color: #90caf9;
    }
  }
  .form-row {
    display: flex;
    gap: 18px;
    align-items: flex-end;
    margin-bottom: 14px;
    .connect-btn {
      font-weight: 700;
      background: #2563eb !important;

      &:hover {
        box-shadow: var(--box-shadow);
      }
    }
    .paste-btn {
      padding: 4px;
      background-color: var(--background-color-200);
    }
  }
}
</style>
