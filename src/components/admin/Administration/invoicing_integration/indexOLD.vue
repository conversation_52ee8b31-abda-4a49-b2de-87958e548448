<script>
//import { getRandomValues, randomBytes } from 'crypto';
import MsBusinessCentralConnectCard from '@/components/common/MsBusinessCentralConnectCard.vue';
//var crypto = require('crypto');
//import { crypto } ;
//@Component({
//  components: {
//    InvoicingIntegration,
//  },
//})
//import { ref, computed } from 'vue';
//const quickbooks = True;

export default {
  components: {
    MsBusinessCentralConnectCard,
  },
  data() {
    return {
      pkce_code: String,
      pkce_challenge: String,
      //message: 'Hello world',
    };
  },
  beforeMount() {
    this.generate_pkce_codes();
    //this.qb_exchange_code_for_tokens();
  },
  methods: {
    /*generateVerifier = () => {
            return crypto.randomBytes(64).toString('hex');
        },
        getChallenge = verifier => {
            return crypto.createHash('sha256')
            .update(verifier)
            .digest('base64')
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '')
        },
        generateRandomBase64String = async (length = 24) => 
            Buffer.from(crypto.getRandomValues(new Uint8Array(length))).toString('base64url'),
        computeCodeChallengeFromVerifier = async (verifier: string) => {
            const hashedValue = await crypto.subtle.digest(
            'SHA-256',
            new TextEncoder().encode(verifier)
            );
            return Buffer.from(hashedValue).toString('base64url');
        },
        isCodeVerifierValid = async (codeVerifier: string, codeChallenge: string) => 
            (await computeCodeChallengeFromVerifier(codeVerifier)) === codeChallenge, */
    test() {
      console.log('Clicked it!!');
    },
    initiate_qb_auth() {
      console.log('Triggered initiate_qb_auth()');
      const qb_auth_link =
        'https://appcenter.intuit.com/connect/oauth2?response_type=code&client_id=ABfMT3Pq1LiUhPz2Vuiz5tBRQ4OgqbBkUXbIk2PeAg4hiqhC08&state=21a1cbd94d6c404bab017d9ab828b48b&scope=com.intuit.quickbooks.accounting&redirect_uri=http%3A%2F%2Flocalhost%3A8082%2Fadministration%2Finvoicing_integration_callback';
      window.open(qb_auth_link, '_blank');
    },
    generate_pkce_codes() {
      function dec2hex(dec) {
        return ('0' + dec.toString(16)).substr(-2);
      }

      function generateRandomString() {
        var array = new Uint32Array(56 / 2);
        //window.crypto.getRandomValues(array);
        crypto.getRandomValues(array);
        return Array.from(array, dec2hex).join('');
      }

      function sha256(plain) {
        // returns promise ArrayBuffer
        const encoder = new TextEncoder();
        const data = encoder.encode(plain);
        //return window.crypto.subtle.digest('SHA-256', data);
        return window.crypto.subtle.digest('SHA-256', data);
      }

      function base64urlencode(a) {
        var str = '';
        var bytes = new Uint8Array(a);
        var len = bytes.byteLength;
        for (var i = 0; i < len; i++) {
          str += String.fromCharCode(bytes[i]);
        }
        return btoa(str)
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=+$/, '');
      }

      async function challenge_from_verifier(v) {
        let hashed = await sha256(v);
        let base64encoded = base64urlencode(hashed);
        return base64encoded;
      }

      const code = generateRandomString();
      challenge_from_verifier(code).then((value) => {
        console.log('Fetched challenge_from_verifier successfully.');
        console.log('Obtained PKCE challenge: ' + value);
        this.pkce_code = code;
        this.pkce_challenge = value;
      });
    },
    initiate_ms_auth() {
      console.log('Triggered initiate_ms_auth()');

      let tenant_id = 'common';
      const ms_auth_link =
        'https://login.microsoftonline.com/' +
        tenant_id +
        '/oauth2/v2.0/authorize?client_id=b8bc9659-0805-4b02-959a-aa5673c87892&response_type=code&redirect_uri=http%3A%2F%2Flocalhost%3A8082%2Fadministration%2Finvoicing_integration_callback_ms&response_mode=query&scope=https%3A%2F%2Fapi.businesscentral.dynamics.com%2F%2F.default&state=12345&code_challenge=' +
        this.pkce_challenge +
        '&code_challenge_method=S256';

      // At this point we must store the code_verifier within the app's memory at least short term, until we make the request for the auth_code
      localStorage.pkce_code = this.pkce_code;
      window.open(ms_auth_link, '_blank');
    },
    handleMSConnection(data) {
      console.log('Successfully connected to MS Business Central:', data);
      // You can add logic here to refresh data or update UI
    },
    handleMSConnectionFailure(error) {
      console.error('Failed to connect to MS Business Central:', error);
      // You can show a notification to the user
    },
  },
};

/*    name: "App",
    data() {
        return {};
    },
    methods: {
        initiate_qb_auth: function() {
            console.log("Triggered initiate_qb_auth()");
            qb_auth_link = "https://appcenter.intuit.com/connect/oauth2?response_type=code&client_id=ABfMT3Pq1LiUhPz2Vuiz5tBRQ4OgqbBkUXbIk2PeAg4hiqhC08&state=21a1cbd94d6c404bab017d9ab828b48b&scope=com.intuit.quickbooks.accounting&redirect_uri=https%3A%2F%2Fdeveloper.intuit.com%2Fv2%2FOAuth2Playground%2FRedirectUrl";
            window.open(qb_auth_link, '_blank');
        }
    }
}; */

/*function initiate_qb_auth() {
  console.log("Triggered initiate_qb_auth()");
  qb_auth_link = "https://appcenter.intuit.com/connect/oauth2?response_type=code&client_id=ABfMT3Pq1LiUhPz2Vuiz5tBRQ4OgqbBkUXbIk2PeAg4hiqhC08&state=21a1cbd94d6c404bab017d9ab828b48b&scope=com.intuit.quickbooks.accounting&redirect_uri=https%3A%2F%2Fdeveloper.intuit.com%2Fv2%2FOAuth2Playground%2FRedirectUrl";

  window.open(qb_auth_link, '_blank');
} */
</script>

<template>
  <v-layout wrap class="reports-container px-3">
    <div class="top-panel">
      <v-spacer></v-spacer>
    </div>
    <v-flex
      md12
      class="app-bgcolor--400 app-borderside--a-2x app-bordercolor--600 mb-3"
    >
      <div class="pa-2 header-text">Accounting Integration</div>
      <v-divider class="ma-0 pa-0" />
      <v-form class="pa-3" @submit.prevent="initiate_ms_auth">
        <!-- ~~~ We should populate this list from elsewhere, eg by reading from a list of supported accounting packages ~~~ -->
        <!--

      <v-select
        v-model="selectedServiceProvider"
        :items="['None', 'Quickbooks', 'Microsoft Dynamics 365']"
        label="Choose your accounting service"
        hint="Please choose the accounting package that you wish to connect to"
        persistent-hint
        color="primary"
        solo
        flat
        class="v-solo-custom mb-2"
        return-object
        @change="setSelectedServiceProvider"
      >
      </v-select>
      -->

        <div class="connect-to-provider">
          <!-- </div>v-if="quickbooks"> -->
          <i class="fal fa-key"></i>
          <button @click="initiate_qb_auth">Connect to QB</button>
        </div>

        <div class="connect-to-provider">
          <!-- </div>v-if="quickbooks"> -->
          <i class="fal fa-key"></i>
          <button @click="initiate_ms_auth">Connect to MS Dynamics</button>
        </div>
        <ms-business-central-connect-card
          class="mt-3"
          @connection-successful="handleMSConnection"
          @connection-failed="handleMSConnectionFailure"
        />
      </v-form>
    </v-flex>
    <!-- <p class="greeting">{{ greeting }}</p>  -->
  </v-layout>
</template>

<style>
.greeting {
  color: red;
  font-weight: bold;
}
.connect-to-provider {
  font-size: large;
  i {
    color: red;
    padding: 10px;
  }
}
</style>
