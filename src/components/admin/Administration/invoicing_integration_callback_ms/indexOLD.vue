<script>
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import GetTokensFromOauth2AuthCodeRequest from '@/interface-models/InvoicingIntegration/GetTokensFromOauth2AuthCodeRequest';
import { ref, computed } from 'vue';

const bcUrl = ref('');

export default {
  data() {
    return {
      bcUrl: '', // It seems that we don't need to declare it as bcUrl: ref('')
      bcTokenId: computed(() => {
        try {
          const url = new URL(this.bcUrl);
          const parts = url.pathname.split('/');
          return parts[1] || '';
        } catch {
          return '';
        }
      }),
      bcEnvironment: computed(() => {
        try {
          const url = new URL(this.bcUrl);
          const parts = url.pathname.split('/');
          return parts[2] || '';
        } catch {
          return '';
        }
      }),
    };
  },
  methods: {
    /*filterBCurlForEnv() {
      return("123");
    },*/
    ms_exchange_code_for_tokens() {
      function convert_js_urlencoded(jsonData) {
        const urlEncodedString = Object.entries(jsonData)
          .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
          .join('&');
        return urlEncodedString;
      }

      if (this.bcTokenId == '' || this.bcEnvironment == '') {
        console.log(
          "You can't proceed without a valid tokenId and environment name",
        );
        return;
      }

      console.log('Sending request to MS for tokens');

      // Define POST requet to QB to obtain tokens...
      const auth_code = this.$route.query.code;

      if (localStorage.pkce_code && auth_code) {
        // Required POST data
        const godesta_app_id = '1cab1e6d-1644-4a12-a1f1-cbde3ddce409';
        const godesta_client_id = 'b8bc9659-0805-4b02-959a-aa5673c87892';
        const godesta_client_secret =
          '****************************************';
        const redirect_uri =
          'http%3A%2F%2Flocalhost%3A8082%2Fadministration%2Finvoicing_integration_callback_ms';

        let postData = {
          client_id: godesta_client_id,
          code: auth_code,
          redirect_uri:
            'http://localhost:8082/administration/invoicing_integration_callback_ms',
          grant_type: 'authorization_code',
          code_verifier: localStorage.pkce_code,
          //'scope': 'https://api.businesscentral.dynamics.com//.default' // <~~~~
        };

        const ms_token_link =
          'https://login.microsoftonline.com/' +
          'common' +
          '/oauth2/v2.0/token'; // + auth_code + "&redirect_uri=" + redirect_uri;
        console.log('8');
        const qb_basicauthtoken =
          'Basic ' + btoa(godesta_client_id + ':' + godesta_client_secret);
        const qb_headers = new Headers();
        qb_headers.append('Content-Type', 'application/x-www-form-urlencoded');
        qb_headers.append('Accept', 'application/json');
        qb_headers.append('Origin', 'http://localhost:8082');

        const request = new Request(ms_token_link, {
          method: 'POST',
          headers: qb_headers,
          mode: 'cors', // cors is the default
          //cache: 'default',
          body: convert_js_urlencoded(postData),
        });

        console.log(
          'Request Method: ' +
            request.method +
            "\nHeaders: '" +
            request.headers.get('Content-Type') +
            "'\n'" +
            request.headers.get('Accept') +
            "'\n Authorization: '" +
            request.headers.get('Authorization') +
            '\n Origin: ' +
            request.headers.get('Origin') +
            "'\nMode: " +
            request.mode +
            '\nCache: ' +
            request.cache,
        );

        fetch(request)
          .then((response) => {
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            return response.json();
          })
          .then((data) => {
            let access_token = '';
            let refresh_token = '';

            let response_json_data = data;
            console.log(
              'Successful response from MS containing: ' +
                JSON.stringify(response_json_data),
            );
            if (
              response_json_data.hasOwnProperty('access_token') &&
              response_json_data.hasOwnProperty('refresh_token')
            ) {
              access_token = response_json_data['access_token'];
              refresh_token = response_json_data['refresh_token'];
            }

            // Send request over websocket
            console.log(
              '\n\n\n\n\nSending WS request to for /export/saveBCCredentials\n\n\n',
            );

            let getTokensFromOauth2AuthCodePayload =
              new GetTokensFromOauth2AuthCodeRequest(
                access_token,
                refresh_token,
                this.bcTokenId,
                this.bcEnvironment,
              );

            console.log(getTokensFromOauth2AuthCodePayload);
            const result = sendRequestAndListenForResponse(
              new WebSocketRequest(
                //'/export/saveBCCredentials',
                '/export/createExportUser',
                getTokensFromOauth2AuthCodePayload,
                true, // stringify
              ),
              'createExportUser',
            );

            result.then((data) => {
              console.log('Received response from createExportUser: ' + data);
              console.log(data);

              const companyMap = new Map(Object.entries(data.companies));

              // ~~~~~ Create a table here ~~~~~~
              companyMap.keys().forEach((k) => {
                console.log(k + ' -> ' + companyMap.get(k));
              });
            });

            const getExportUsersResult = sendRequestAndListenForResponse(
              new WebSocketRequest(
                '/export/getExportUsersForEditing',
                '',
                true, // stringify
              ),
              'getExportUsersForEditing',
            );

            getExportUsersResult.then((data) => {
              const exportUsersList = Object.entries(data);
              exportUsersList.forEach((d) => {
                console.log(
                  'ExportUser: ' +
                    JSON.stringify(d) +
                    ' with type: ' +
                    typeof d,
                );
                //x = JSON.parse(d.toString());
                console.log(JSON.stringify(d));

                // Update the export user
                const getExportUsersResult = sendRequestAndListenForResponse(
                  new WebSocketRequest(
                    '/export/updateExportUser',
                    d, //getTokensFromOauth2AuthCodePayload,
                    true, // stringify
                  ),
                  'updateExportUser',
                );
              });
              console.log(
                'Retrieved ' + exportUsersList.length + ' current ExportUsers',
              );
              //console.log("ExportUsers list: " + exportUsersList.toString());
              //console.log(exportUsersList);
            });

            return result;
          })
          .catch((error) => {
            console.log('13');
            console.log('Error occurred: ' + error);
          });

        console.log('14');
      } else {
        console.log(
          'Local storage contains no pkce_code value, so we cannot make a code verified request to MS.',
        );
      }
    },
  },
};
</script>

<template>
  <v-layout wrap class="reports-container px-3">
    <div class="top-panel">
      <v-spacer></v-spacer>
    </div>
    <v-flex
      md12
      class="app-bgcolor--400 app-borderside--a-2x app-bordercolor--600 mb-3"
    >
      <div class="pa-2 header-text">Accounting Integration</div>
      <v-divider class="ma-0 pa-0" />
      <v-form class="pa-3" @submit.prevent="ms_exchange_code_for_tokens">
        <!-- ~~~ We should populate this list from elsewhere, eg by reading from a list of supported accounting packages ~~~ -->
        <!--

      <v-select
        v-model="selectedServiceProvider"
        :items="['None', 'Quickbooks', 'Microsoft Dynamics 365']"
        label="Choose your accounting service"
        hint="Please choose the accounting package that you wish to connect to"
        persistent-hint
        color="primary"
        solo
        flat
        class="v-solo-custom mb-2"
        return-object
        @change="setSelectedServiceProvider">
      </v-select>
      -->
        <div
          v-if="
            $route.query.code &&
            $route.query.state &&
            $route.query.session_state
          "
        >
          <table>
            <tr>
              <th style="width: 30%; text-align: left">Name</th>
              <th style="text-align: left">Value</th>
            </tr>
            <tr>
              <td>code</td>
              <td>{{ $route.query.code }}</td>
            </tr>
            <tr>
              <td>state</td>
              <td>{{ $route.query.state }}</td>
            </tr>
            <tr>
              <td>session_state</td>
              <td>{{ $route.query.session_state }}</td>
            </tr>
            <tr>
              <td>URL you use to access Business Central</td>
              <td>
                <input
                  v-model="bcUrl"
                  placeholder="Paste your Business Central URL here"
                />
              </td>
            </tr>
            <tr>
              <td>Business Central token_id</td>
              <td>{{ bcTokenId }}</td>
            </tr>
            <tr>
              <td>Business Central Environment</td>
              <td>{{ bcEnvironment }}</td>
            </tr>
          </table>

          <div class="connect-to-provider">
            <!-- </div>v-if="quickbooks"> -->
            <i class="fal fa-key"></i>
            <button @click="ms_exchange_code_for_tokens()">
              <!-- button @click="test()" -->
              Exchange MS auth code for tokens
            </button>
          </div>

          <div class="token-details"></div>
        </div>
        <div v-else>
          <div v-if="!$route.query.code">No authcode was received.</div>
          <div v-if="!$route.query.state">No state was received.</div>
          <div v-if="!$route.query.session_state">
            No session_state was received.
          </div>
        </div>
      </v-form>
    </v-flex>
    <!-- <p class="greeting">{{ greeting }}</p>  -->
  </v-layout>
</template>

<style>
.greeting {
  color: red;
  font-weight: bold;
}
.connect-to-provider {
  font-size: large;
  i {
    color: red;
    padding: 10px;
  }
}
</style>
