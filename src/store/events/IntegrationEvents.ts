import { CustomerDataSnapshot } from '@/interface-models/InvoicingIntegration/CustomerDataSnapshot';
import { ExportTypeConfig } from '@/interface-models/InvoicingIntegration/ExportTypeConfig';
import {
  CreateExportUserResponse,
  ExportUser,
} from '@/interface-models/InvoicingIntegration/ExportUser';

export type IntegrationEvents = {
  /**
   * WebSocket event for creating an export user (MS Dynamics integration).
   * @request
   * * payload:   GetTokensFromOauth2AuthCodeRequest
   * * endpoint:  '/export/createExportUser'
   * @response
   * * payload:   ExportUser
   * * id:        createExportUser
   * @portal OPERATIONS
   * @subscription USER
   */
  createExportUser: CreateExportUserResponse;

  /**
   * WebSocket event for retrieving export users for editing (MS Dynamics integration).
   * @request
   * * payload:
   * * endpoint:  '/export/getExportUsersForEditing'
   * @response
   * * payload:   ExportUser[]
   * * id:        getExportUsersForEditing
   * @portal OPERATIONS
   * @subscription USER
   */
  getExportUsersForEditing: ExportUser[];

  /**
   * WebSocket event for updating an export user (MS Dynamics integration).
   * @request
   * * payload:   GetTokensFromOauth2AuthCodeRequest
   * * endpoint:  '/export/updateExportUser'
   * @response
   * * payload:   ExportUser
   * * id:        updateExportUser
   * @portal OPERATIONS
   * @subscription USER
   */
  updateExportUser: ExportUser;

  /**
   * WebSocket event for updating export user token information (MS Dynamics integration).
   * @request
   * * payload:  GetTokensFromOauth2AuthCodeRequest
   * * endpoint:  '/export/updateExportUserToken'
   * @response
   * * payload:   ExportUser
   * * id:        updateExportUserToken
   * @portal OPERATIONS
   * @subscription USER
   */
  updateExportUserToken: ExportUser;

  /**
   * WebSocket event for getting configs for MS Dynamics integration.
   * @request
   * * payload:
   * * endpoint:  '/export/getExportTypes'
   * @response
   * * payload:   ExportTypeConfig
   * * id:        getExportTypes
   * @portal OPERATIONS
   * @subscription USER
   */
  getExportTypes: ExportTypeConfig;

  /**
   * WebSocket event for getting customer list from export services.
   * @request
   * * payload:
   * * endpoint:  '/export/getCustomerListFromExportServices'
   * @response
   * * payload:   CustomerDataSnapshot
   * * id:        getCustomerListFromExportServices
   * @portal OPERATIONS
   * @subscription USER
   */
  getCustomerListFromExportServices: CustomerDataSnapshot;

  /**
   * WebSocket event for refreshing customer list from export services.
   * @request
   * * payload:
   * * endpoint:  '/export/refreshCustomerListFromExportServices'
   * @response
   * * payload:   CustomerDataSnapshot
   * * id:        refreshCustomerListFromExportServices
   * @portal OPERATIONS
   * @subscription USER
   */
  refreshCustomerListFromExportServices: CustomerDataSnapshot;
};
